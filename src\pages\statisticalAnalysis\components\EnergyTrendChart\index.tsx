import * as React from 'react';
import { Table, Modal } from 'antd';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import style from './index.module.less';
import { ECBasicOption } from 'echarts/types/dist/shared';

// 注册ECharts组件
echarts.use([LineChart, GridComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

/**
 * 能耗趋势图表组件属性接口
 */
interface EnergyTrendChartProps {
  /** 额外的CSS类名 */
  className?: string;
}

/**
 * 能耗趋势图表组件
 *
 * 使用ECharts折线图显示企业能耗随时间变化的趋势
 * 点击"更多"按钮可查看详细的月度能耗数据表格
 *
 * @param props - 组件属性
 * @returns 能耗趋势图表组件
 */
const EnergyTrendChart: React.FC<EnergyTrendChartProps> = ({ className }) => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstance = React.useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = React.useState(false);

  // 模拟详细数据 - 各月份能耗量
  const detailData = [
    { key: '1', month: '2024-01', energy: 1420, unit: '万tce' },
    { key: '2', month: '2024-02', energy: 1380, unit: '万tce' },
    { key: '3', month: '2024-03', energy: 1450, unit: '万tce' },
    { key: '4', month: '2024-04', energy: 1390, unit: '万tce' },
    { key: '5', month: '2024-05', energy: 1410, unit: '万tce' },
    { key: '6', month: '2024-06', energy: 1384, unit: '万tce' },
  ];

  // ECharts配置选项
  const chartOption: ECBasicOption = React.useMemo(
    () => ({
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}<br/>${data.seriesName}: ${data.value} 万tce`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: detailData.map((item) => item.month.substring(5)), // 只显示月份
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        axisLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '万tce',
        nameTextStyle: {
          color: '#8c8c8c',
          fontSize: 12,
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 12,
          formatter: '{value}',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: '#f5f5f5',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '能耗量',
          type: 'line',
          smooth: true,
          data: detailData.map((item) => item.energy),
          lineStyle: {
            color: '#1890ff',
            width: 2,
          },
          itemStyle: {
            color: '#1890ff',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.2)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.05)' },
              ],
            },
          },
        },
      ],
    }),
    [detailData],
  );

  // 初始化图表
  React.useEffect(() => {
    if (chartRef.current) {
      chartInstance.current = echarts.init(chartRef.current);
      chartInstance.current.setOption(chartOption);
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, []);

  // 更新图表配置
  React.useEffect(() => {
    if (chartInstance.current) {
      chartInstance.current.setOption(chartOption);
    }
  }, [chartOption]);

  // 处理窗口大小变化
  React.useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  /**
   * 处理"更多"按钮点击事件
   */
  const handleMoreClick = () => {
    setIsModalVisible(true);
  };

  /**
   * 处理Modal关闭事件
   */
  const handleModalClose = () => {
    setIsModalVisible(false);
  };

  // 详细数据表格列配置
  const columns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
    },
    {
      title: '能耗量',
      dataIndex: 'energy',
      key: 'energy',
      render: (value: number) => `${value.toLocaleString()}`, // 格式化数字显示
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
  ];

  const modalContent = (
    <div>
      <p style={{ marginBottom: 16, color: '#595959' }}>
        以下是能耗趋势的详细数据，包含各月份的具体能耗量：
      </p>
      <Table columns={columns} dataSource={detailData} pagination={false} size="small" bordered />
    </div>
  );

  return (
    <>
      <div className={`${style['energy-trend-chart']} ${className || ''}`}>
        {/* 图表标题栏 */}
        <div className={style['chart-header']}>
          <div className={style['chart-title']}>能耗趋势</div>
          <div
            className={style['chart-more']}
            onClick={handleMoreClick}
            onKeyDown={(e) => e.key === 'Enter' && handleMoreClick()}
            role="button"
            tabIndex={0}
          >
            更多
          </div>
        </div>

        {/* ECharts图表容器 */}
        <div ref={chartRef} className={style['chart-container']} />
      </div>

      {/* 详细数据Modal */}
      <Modal
        title="能耗趋势 - 详细数据"
        visible={isModalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        className={style['chart-modal']}
      >
        {modalContent}
      </Modal>
    </>
  );
};

export default EnergyTrendChart;
